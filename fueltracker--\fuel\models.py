from django.core.exceptions import ValidationError
from django.db import models
import random
from datetime import date

def validate_date_range(value):
    if value < date(2024, 10, 13):  # Only lower bound enforced
        raise ValidationError('Date must be on or after 2024-10-13')

class Driver(models.Model):
    name = models.CharField(max_length=100, unique=True)
    vehicle = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.name} - {self.vehicle}"

    class Meta:
        verbose_name = "Driver"
        verbose_name_plural = "Drivers"

class Vehicle(models.Model):
    name = models.CharField(max_length=100)
    plate_number = models.CharField(max_length=20)
    
    def __str__(self):
        return f"{self.name} ({self.plate_number})"

class FuelConsumption(models.Model):
    reference_number = models.PositiveIntegerField(default=1)

    # Destination choices for ambulances (excludes local area)
    AMBULANCE_DESTINATION_CHOICES = [
        ('dipolog', 'Dipolog'),
        ('cagayan', '<PERSON>agayan'),
        ('margosatubig', 'Margosatubig'),
        ('pagadian_city', 'Pagadian City'),
        ('ozamiz_city', 'Ozamiz City'),
        ('zamboanga_city', 'Zamboanga City'),
        ('ipil', 'Ipil'),
        ('sindangan', 'Sindangan'),
    ]

    # Destination choices for heavy equipment (local area only)
    HEAVY_EQUIPMENT_DESTINATION_CHOICES = [
        ('local', 'Local Area'),
    ]

    # Combined choices for the model field (for backward compatibility)
    DESTINATION_CHOICES = AMBULANCE_DESTINATION_CHOICES + HEAVY_EQUIPMENT_DESTINATION_CHOICES

    PURPOSE_CHOICES = [
        ('Transport Patient', 'Transport Patient'),
        ('landslide sinonok', 'Landslide Sinonok'),
        ('landslide bag-ong kauswagan', 'Landslide Bag-ong Kauswagan'),
        ('landslide macasing', 'Landslide Macasing'),
    ]

    driver = models.ForeignKey('Driver', on_delete=models.CASCADE)
    date = models.DateField()
    trip_number = models.PositiveIntegerField(default=1)
    number_of_trips = models.PositiveIntegerField(default=1)
    purpose = models.CharField(max_length=255, choices=PURPOSE_CHOICES, default="Transport Patient")
    destination = models.CharField(max_length=100, choices=DESTINATION_CHOICES, default='local')
    total_liters = models.FloatField()
    cost = models.FloatField()
    vehicle = models.CharField(max_length=100)
    MIN_LITERS_PER_TRIP = 17.69
    MAX_LITERS_PER_TRIP = 26.54
    FUEL_PRICE = 62.00 #per liter
    TOTAL_FUEL_ALLOCATION = 5017.78  # 311062.35 / 62.00 ≈ 5017.78 liters

    # Destination-based fuel budget (in pesos) - updated for ₱62.00 fuel price
    DESTINATION_BUDGET_MAP = {
        'dipolog': 2720,  # Adjusted for new fuel price
        'cagayan': 5440,  # Adjusted for new fuel price
        'margosatubig': 2176,  # Adjusted for new fuel price
        'pagadian_city': 1088,  # Adjusted for new fuel price
        'ozamiz_city': 1632,  # Adjusted for new fuel price
        'zamboanga_city': 5440,  # Adjusted for new fuel price
        'ipil': 3808,  # Adjusted for new fuel price
        'sindangan': 1632,  # Adjusted for new fuel price
        'local': 2176,  # Default budget for local trips (adjusted)
    }

    # Fixed fuel consumption for heavy equipment (in liters)
    HEAVY_EQUIPMENT_FUEL = 400  # Backhoe and Dumptruck consume 400L each

    def get_destination_budget(self):
        """Get the fuel budget (in pesos) for the current destination"""
        return self.DESTINATION_BUDGET_MAP.get(self.destination, self.DESTINATION_BUDGET_MAP['local'])

    def get_destination_liters(self):
        """Calculate liters based on destination budget and fuel price"""
        budget = self.get_destination_budget()
        return round(budget / self.FUEL_PRICE, 2)

    def __str__(self):
        return f"{self.driver} on {self.date} (Trip {self.trip_number})"

    class Meta:
        unique_together = ('driver', 'date', 'trip_number')

    def clean(self):
        """Validate that destination is appropriate for vehicle type"""
        super().clean()

        # Check if ambulance is trying to use local destination
        if (hasattr(self, 'vehicle') and
            self.vehicle in ['Ambulance L300', 'Ambulance Province', 'Ambulance DOH'] and
            self.destination == 'local'):
            raise ValidationError("Ambulances cannot use 'Local Area' destination. Local Area is reserved for heavy equipment only.")

        # Check if heavy equipment is trying to use non-local destination
        if (hasattr(self, 'vehicle') and
            self.vehicle in ['Backhoe', 'Dumptruck'] and
            self.destination != 'local'):
            raise ValidationError("Heavy equipment (Backhoe/Dumptruck) can only use 'Local Area' destination.")

    def save(self, *args, **kwargs):
        if not self.total_liters:
            self._calculate_fuel_usage()

        self.clean()
        self._check_fuel_limits()
        super().save(*args, **kwargs)

    def _calculate_fuel_usage(self):
        if not self.number_of_trips:
            raise ValidationError("Number of trips must be set")

        # Check if this is heavy equipment (Backhoe or Dumptruck)
        if hasattr(self, 'vehicle') and self.vehicle in ['Backhoe', 'Dumptruck']:
            # Fixed 400 liters for heavy equipment
            total_liters = self.HEAVY_EQUIPMENT_FUEL * self.number_of_trips
            total_cost = total_liters * self.FUEL_PRICE
        elif hasattr(self, 'destination') and self.destination:
            # Use destination-based fuel budget for ambulances
            budget_per_trip = self.DESTINATION_BUDGET_MAP.get(self.destination, self.DESTINATION_BUDGET_MAP['local'])
            total_cost = budget_per_trip * self.number_of_trips
            total_liters = total_cost / self.FUEL_PRICE
        else:
            # Fallback to random calculation for backward compatibility
            total_liters = sum(
                round(random.uniform(self.MIN_LITERS_PER_TRIP, self.MAX_LITERS_PER_TRIP), 2)
                for _ in range(self.number_of_trips)
            )
            total_cost = total_liters * self.FUEL_PRICE

        self.total_liters = round(total_liters, 2)
        self.cost = round(total_cost, 2)

    def _check_fuel_limits(self):
        queryset = FuelConsumption.objects.exclude(pk=self.pk)
        total_consumed = queryset.aggregate(
            models.Sum('total_liters')
        )['total_liters__sum'] or 0

        if self.pk:
            old_instance = FuelConsumption.objects.get(pk=self.pk)
            total_consumed -= old_instance.total_liters

        proposed_total = total_consumed + self.total_liters

        if proposed_total > self.TOTAL_FUEL_ALLOCATION:
            remaining_fuel = self.TOTAL_FUEL_ALLOCATION - total_consumed
            raise ValidationError(
                f"Insufficient fuel! Available: {remaining_fuel:.2f}L, "
                f"Requested: {self.total_liters:.2f}L"
            )

    def __str__(self):
        return (f"{self.driver} | {self.date.strftime('%Y-%m-%d')} | "
                f"{self.number_of_trips} trips | {self.total_liters}L")